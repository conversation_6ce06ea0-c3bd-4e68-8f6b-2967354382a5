{"name": "conversations", "fields": [{"name": "id", "type": "string"}, {"name": "created_at", "type": "int64"}, {"name": "started_at", "type": "int64", "optional": true}, {"name": "finished_at", "type": "int64", "optional": true}, {"name": "structured", "type": "object"}, {"name": "structured.title", "type": "string", "optional": true}, {"name": "structured.overview", "type": "string", "optional": true}, {"name": "structured.category", "type": "string", "facet": true, "optional": true}, {"name": "discarded", "type": "bool"}, {"name": "deleted", "type": "bool", "optional": true}, {"name": "userId", "type": "string"}], "default_sorting_field": "created_at", "enable_nested_fields": true}